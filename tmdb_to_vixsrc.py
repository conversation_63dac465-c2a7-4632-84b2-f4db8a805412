#!/usr/bin/env python3

import argparse
import json
import sys
import requests
from bs4 import BeautifulSoup
import re

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

def get_tmdb_details(tmdb_id, api_key, is_tv=False):
    url = f"https://api.themoviedb.org/3/{'tv' if is_tv else 'movie'}/{tmdb_id}?api_key={api_key}"
    try:
        r = requests.get(url, timeout=5)
        r.raise_for_status()
        data = r.json()

        title = data.get('name') if is_tv else data.get('title')
        date_field = data.get('first_air_date' if is_tv else 'release_date')
        year = date_field[:4] if date_field else None

        return {'title': title, 'year': year}
    except Exception as e:
        print(f"[ERROR] TMDB request failed: {e}", file=sys.stderr)
        return None

def search_vixsrc(title, year=None, is_tv=False, tmdb_id=None):
    if tmdb_id:
        url = f"https://vixsrc.to/{'tv' if is_tv else 'movie'}/{tmdb_id}/"
        if is_tv:
            url += "1/1/"
        try:
            r = requests.get(url, headers=HEADERS, timeout=5)
            if r.status_code == 200:
                print(f"[INFO] Found direct match via TMDB ID: {url}", file=sys.stderr)
                return tmdb_id
        except Exception as e:
            print(f"[WARN] Direct TMDB ID check failed: {e}", file=sys.stderr)

    query = requests.utils.quote(f"{title} {year}" if year else title)
    search_url = f"https://vixsrc.to/search?keyword={query}&vt={'tv' if is_tv else 'movie'}"

    try:
        r = requests.get(search_url, headers=HEADERS, timeout=5)
        r.raise_for_status()
        soup = BeautifulSoup(r.text, 'html.parser')
        cards = soup.select('.flw-item')
        results = []

        for card in cards:
            try:
                a_tag = card.select_one('.film-name a')
                if not a_tag:
                    continue
                result_title = a_tag.text.strip()
                url = a_tag.get('href', '')
                id_match = re.search(r'/tv/(\d+)(?:/\d+/\d+)?' if is_tv else r'/movie/(\d+)', url)
                if not id_match:
                    continue
                result_id = id_match.group(1)
                year_elem = card.select_one('.fd-infor .fdi-item')
                result_year = re.search(r'(\d{4})', year_elem.text).group(1) if year_elem else None

                results.append({
                    'id': result_id,
                    'title': result_title,
                    'year': result_year,
                    'url': url
                })
            except:
                continue

        # Prefer exact year match
        if year:
            for res in results:
                if res.get('year') == str(year):
                    return res['id']

        return results[0]['id'] if results else tmdb_id

    except Exception as e:
        print(f"[ERROR] Failed to search vixsrc.to: {e}", file=sys.stderr)
        return tmdb_id if tmdb_id else None

def tmdb_to_vixsrc(tmdb_id, api_key, is_tv=False):
    details = get_tmdb_details(tmdb_id, api_key, is_tv)
    if not details or not details['title']:
        print(f"[ERROR] No TMDB details for ID {tmdb_id}", file=sys.stderr)
        return None
    return search_vixsrc(details['title'], details['year'], is_tv, tmdb_id)

def main():
    parser = argparse.ArgumentParser(description='Convert TMDB ID to vixsrc ID')
    parser.add_argument('tmdb_id', help='TMDB ID')
    parser.add_argument('--api-key', required=True, help='TMDB API key')
    parser.add_argument('--tv', action='store_true', help='Set if content is a TV show')
    parser.add_argument('--season', type=int, help='Optional season number')
    parser.add_argument('--episode', type=int, help='Optional episode number')
    args = parser.parse_args()

    vixsrc_id = tmdb_to_vixsrc(args.tmdb_id, args.api_key, args.tv)

    if vixsrc_id:
        result = {
            'tmdb_id': args.tmdb_id,
            'vixsrc_id': vixsrc_id,
            'is_tv': args.tv,
            'season': args.season,
            'episode': args.episode
        }
        print(json.dumps(result))
        return 0
    else:
        print(json.dumps({'error': f"Failed to convert TMDB ID {args.tmdb_id} to vixsrc ID"}))
        return 1

if __name__ == "__main__":
    sys.exit(main())
