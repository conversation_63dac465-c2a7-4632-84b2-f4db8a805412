import undetected_chromedriver as uc
import json
import argparse
import sys
import re
import time

def extract_early_exit(video_id, is_tv=False, season=None, episode=None, timeout=7):
    if is_tv:
        if '/' in video_id:
            url = f"https://vixsrc.to/tv/{video_id}/"
        else:
            season = season or 1
            episode = episode or 1
            url = f"https://vixsrc.to/tv/{video_id}/{season}/{episode}/"
    else:
        url = f"https://vixsrc.to/movie/{video_id}/"

    print(f"[INFO] Accessing URL: {url}")

    options = uc.ChromeOptions()
    options.add_argument('--headless=new')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-gpu')
    options.add_argument('--disable-dev-shm-usage')
    options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})

    driver = uc.Chrome(options=options)

    try:
        driver.get(url)

        start = time.time()
        found_url = None

        # Poll i log ogni 0.5s per massimo timeout secondi
        while time.time() - start < timeout:
            logs = driver.get_log('performance')
            for entry in logs:
                try:
                    msg = json.loads(entry['message'])['message']
                    if msg.get('method') == 'Network.responseReceived':
                        response = msg['params']['response']
                        net_url = response.get('url', '')
                        mime = response.get('mimeType', '').lower()
                        if '.m3u8' in net_url or 'mpegurl' in mime:
                            found_url = net_url
                            break
                except:
                    continue
            if found_url:
                break
            time.sleep(0.5)

        # Fallback rapido se non trovato nei log
        if not found_url:
            html = driver.page_source
            urls = re.findall(r'https?://[^\s\'"]+\.m3u8[^\s\'"]*', html)
            if urls:
                found_url = urls[0]

        return found_url
    finally:
        driver.quit()

def main():
    parser = argparse.ArgumentParser(description='Estrai URL .m3u8 velocemente da vixsrc.to')
    parser.add_argument('video_id', help='ID o URL del video')
    parser.add_argument('--tv', action='store_true')
    parser.add_argument('--movie', action='store_true')
    parser.add_argument('--extract-only', action='store_true')
    parser.add_argument('--season', type=int)
    parser.add_argument('--episode', type=int)
    args = parser.parse_args()

    video_id = args.video_id
    is_tv = args.tv

    if video_id.startswith('http'):
        if 'vixsrc.to/tv/' in video_id:
            is_tv = True
            video_id = video_id.split('vixsrc.to/tv/')[1].rstrip('/')
        elif 'vixsrc.to/movie/' in video_id:
            is_tv = False
            video_id = video_id.split('vixsrc.to/movie/')[1].rstrip('/')

    if '/' in video_id and not args.movie:
        is_tv = True
    if args.movie:
        is_tv = False

    url = extract_early_exit(video_id, is_tv, args.season, args.episode)

    if not url:
        print("Nessun URL trovato entro il timeout")
        return 1

    if args.extract_only:
        if not url.endswith('.m3u8'):
            url += '.m3u8'
        sys.stdout.write(url)
    else:
        print(f"[INFO] URL trovato: {url}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
